/**
 * 测试main.js设置加载功能
 * 验证AI提示词和自备评论库是否能正确加载
 */

console.log("=== 开始测试设置加载功能 ==="); // 测试开始日志

// 模拟douyin.js中的设置加载逻辑
var aiPromptWithDesc = "默认有简介提示词"; // 默认有简介提示词
var aiPromptWithoutDesc = "默认无简介提示词"; // 默认无简介提示词
var customComments = []; // 默认自备评论库

// 从设置文件加载AI提示词设置
function loadAISettings() {
    console.log("开始加载AI设置..."); // 开始加载日志
    
    // 尝试多个可能的路径
    var possiblePaths = [
        files.cwd() + "/douyin_settings.json", // 当前工作目录
        files.path("./douyin_settings.json"), // 相对路径
        files.getSdcardPath() + "/脚本/douyin_settings.json", // SD卡路径
        "/sdcard/脚本/douyin_settings.json" // 绝对路径
    ];

    var settingsLoaded = false; // 设置加载标志

    // 尝试从每个可能的路径加载设置
    for (var i = 0; i < possiblePaths.length; i++) {
        var settingsFile = possiblePaths[i]; // 当前尝试的设置文件路径
        console.log("尝试从路径加载设置: " + settingsFile); // 输出尝试路径

        if (files.exists(settingsFile)) {
            try {
                var settingsStr = files.read(settingsFile); // 读取设置文件
                console.log("读取到设置内容: " + settingsStr); // 输出设置内容
                
                var settings = JSON.parse(settingsStr); // 解析JSON设置
                console.log("解析设置成功"); // 解析成功日志

                // 更新AI提示词设置
                if (settings.aiPromptWithDesc) {
                    aiPromptWithDesc = settings.aiPromptWithDesc; // 更新有简介提示词
                    console.log("✓ 加载有简介AI提示词: " + (aiPromptWithDesc.length > 50 ? aiPromptWithDesc.substring(0, 50) + "..." : aiPromptWithDesc));
                }
                
                if (settings.aiPromptWithoutDesc) {
                    aiPromptWithoutDesc = settings.aiPromptWithoutDesc; // 更新无简介提示词
                    console.log("✓ 加载无简介AI提示词: " + (aiPromptWithoutDesc.length > 50 ? aiPromptWithoutDesc.substring(0, 50) + "..." : aiPromptWithoutDesc));
                }
                
                // 处理自备评论库（从字符串转换为数组）
                if (settings.customComments && settings.customComments.trim() !== "") {
                    customComments = settings.customComments.split('\n').filter(function(comment) {
                        return comment.trim() !== ""; // 过滤空行
                    });
                    console.log("✓ 加载自备评论库，共 " + customComments.length + " 条评论:"); // 输出评论数量
                    for (var j = 0; j < customComments.length; j++) {
                        console.log("  " + (j + 1) + ". " + customComments[j]); // 输出每条评论
                    }
                } else {
                    customComments = []; // 如果没有自备评论，使用空数组
                    console.log("⚠️ 未设置自备评论，将使用AI模块默认评论"); // 警告日志
                }

                settingsLoaded = true; // 设置加载成功标志
                break; // 成功加载设置后退出循环
            } catch (e) {
                console.error("从 " + settingsFile + " 加载设置失败: " + e); // 输出错误信息
            }
        } else {
            console.log("未找到设置文件: " + settingsFile); // 文件不存在日志
        }
    }

    if (!settingsLoaded) {
        console.log("⚠️ 所有路径都未找到设置文件，使用默认AI设置"); // 使用默认设置日志
    }

    return settingsLoaded; // 返回加载结果
}

// 测试AI提示词选择逻辑
function testAIPromptSelection() {
    console.log("\n=== 测试AI提示词选择逻辑 ==="); // 测试标题
    
    // 测试场景1：有视频简介
    var videoDescription1 = "这是一个关于美食的视频，展示了如何制作美味的蛋糕"; // 模拟视频简介
    var hasDescription1 = videoDescription1 && videoDescription1.trim() !== ""; // 检查是否有简介
    var finalAiPrompt1 = ""; // 最终AI提示词
    
    if (hasDescription1) {
        finalAiPrompt1 = aiPromptWithDesc.replace("{description}", videoDescription1); // 替换占位符
        console.log("✓ 场景1 - 有简介，使用专用提示词"); // 输出场景信息
        console.log("视频简介: " + videoDescription1); // 输出视频简介
        console.log("最终提示词: " + (finalAiPrompt1.length > 100 ? finalAiPrompt1.substring(0, 100) + "..." : finalAiPrompt1)); // 输出最终提示词
    }
    
    // 测试场景2：没有视频简介
    var videoDescription2 = ""; // 空简介
    var hasDescription2 = videoDescription2 && videoDescription2.trim() !== ""; // 检查是否有简介
    var finalAiPrompt2 = ""; // 最终AI提示词
    
    if (!hasDescription2) {
        finalAiPrompt2 = aiPromptWithoutDesc; // 使用无简介提示词
        console.log("\n✓ 场景2 - 无简介，使用通用提示词"); // 输出场景信息
        console.log("视频简介: (空)"); // 输出空简介
        console.log("最终提示词: " + finalAiPrompt2); // 输出最终提示词
    }
    
    // 测试场景3：简介只有空格
    var videoDescription3 = "   "; // 只有空格的简介
    var hasDescription3 = videoDescription3 && videoDescription3.trim() !== ""; // 检查是否有简介
    var finalAiPrompt3 = ""; // 最终AI提示词
    
    if (!hasDescription3) {
        finalAiPrompt3 = aiPromptWithoutDesc; // 使用无简介提示词
        console.log("\n✓ 场景3 - 简介只有空格，使用通用提示词"); // 输出场景信息
        console.log("视频简介: (只有空格)"); // 输出简介信息
        console.log("最终提示词: " + finalAiPrompt3); // 输出最终提示词
    }
}

// 测试自备评论库功能
function testCustomComments() {
    console.log("\n=== 测试自备评论库功能 ==="); // 测试标题
    
    if (customComments.length > 0) {
        console.log("✓ 自备评论库可用，共 " + customComments.length + " 条评论"); // 输出评论库信息
        
        // 模拟随机选择评论
        for (var i = 0; i < 3; i++) {
            var randomIndex = Math.floor(Math.random() * customComments.length); // 生成随机索引
            var selectedComment = customComments[randomIndex]; // 选择评论
            console.log("随机选择评论 " + (i + 1) + ": " + selectedComment); // 输出选择的评论
        }
    } else {
        console.log("⚠️ 自备评论库为空，需要在main.js中配置"); // 警告信息
    }
}

// 执行测试
console.log("开始执行测试..."); // 开始执行日志

// 1. 测试设置加载
var loadResult = loadAISettings(); // 加载AI设置
console.log("设置加载结果: " + (loadResult ? "成功" : "失败")); // 输出加载结果

// 2. 测试AI提示词选择
testAIPromptSelection(); // 测试提示词选择

// 3. 测试自备评论库
testCustomComments(); // 测试自备评论库

console.log("\n=== 测试完成 ==="); // 测试完成日志
console.log("✅ 所有测试已完成，请检查上述输出结果"); // 完成提示
