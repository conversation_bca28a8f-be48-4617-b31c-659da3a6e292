"ui"; // 声明使用UI模式，表示此脚本包含用户界面元素

// 定义默认时间变量
var DEFAULT_SETTINGS = {
    likeTimeMin: 3, // 点赞最小时间 3秒
    likeTimeMax: 17, // 点赞最大时间 17秒
    commentTimeMin: 4, // 留言最小时间 4秒
    commentTimeMax: 16, // 留言最大时间 16秒
    watchTimeMin: 5, // 观看最小时间 5秒
    watchTimeMax: 15, // 观看最大时间 15秒
    // AI提示词设置
    aiPromptWithDesc: "你是一个抖音用户，正在观看一个视频，视频简介是：{description}。请根据视频简介生成一条真实自然的评论，要求15字以内，带表情符号，语气要亲切自然。", // 有视频介绍的AI提示词
    aiPromptWithoutDesc: "你是一个抖音用户，正在观看一个有趣的视频，请生成一条通用的正面评论，要求15字以内，带表情符号，语气要亲切自然。", // 没有视频介绍的AI提示词
    customComments: "哇，这个视频太棒了！👍\n看得我都想点赞了 ❤️\n真的很不错呢 😊\n这个我喜欢！✨\n太有意思了 😄" // 自备评论（每行一条）
};

// 定义存储设置的文件路径
var SETTINGS_FILE = files.path("./douyin_settings.json");
console.log("设置文件将保存在: " + SETTINGS_FILE);

// 加载设置
function loadSettings() {
    console.log("尝试加载设置文件: " + SETTINGS_FILE);

    if (files.exists(SETTINGS_FILE)) {
        try {
            // 读取设置文件
            var settingsStr = files.read(SETTINGS_FILE);
            console.log("读取到设置内容: " + settingsStr);

            var settings = JSON.parse(settingsStr);
            console.log("解析设置成功: " + JSON.stringify(settings));

            // 确保所有必要的设置都存在，如果不存在则使用默认值
            settings.likeTimeMin = settings.likeTimeMin !== undefined ? settings.likeTimeMin : DEFAULT_SETTINGS.likeTimeMin;
            settings.likeTimeMax = settings.likeTimeMax !== undefined ? settings.likeTimeMax : DEFAULT_SETTINGS.likeTimeMax;
            settings.commentTimeMin = settings.commentTimeMin !== undefined ? settings.commentTimeMin : DEFAULT_SETTINGS.commentTimeMin;
            settings.commentTimeMax = settings.commentTimeMax !== undefined ? settings.commentTimeMax : DEFAULT_SETTINGS.commentTimeMax;
            settings.watchTimeMin = settings.watchTimeMin !== undefined ? settings.watchTimeMin : DEFAULT_SETTINGS.watchTimeMin;
            settings.watchTimeMax = settings.watchTimeMax !== undefined ? settings.watchTimeMax : DEFAULT_SETTINGS.watchTimeMax;
            // AI提示词设置
            settings.aiPromptWithDesc = settings.aiPromptWithDesc !== undefined ? settings.aiPromptWithDesc : DEFAULT_SETTINGS.aiPromptWithDesc;
            settings.aiPromptWithoutDesc = settings.aiPromptWithoutDesc !== undefined ? settings.aiPromptWithoutDesc : DEFAULT_SETTINGS.aiPromptWithoutDesc;
            settings.customComments = settings.customComments !== undefined ? settings.customComments : DEFAULT_SETTINGS.customComments;

            // 更新UI控件的值
            ui.run(function() {
                ui.likeTimeMinInput.setText(settings.likeTimeMin.toString());
                ui.likeTimeMaxInput.setText(settings.likeTimeMax.toString());
                ui.commentTimeMinInput.setText(settings.commentTimeMin.toString());
                ui.commentTimeMaxInput.setText(settings.commentTimeMax.toString());
                ui.watchTimeMinInput.setText(settings.watchTimeMin.toString());
                ui.watchTimeMaxInput.setText(settings.watchTimeMax.toString());
                // AI提示词控件
                ui.aiPromptWithDescInput.setText(settings.aiPromptWithDesc);
                ui.aiPromptWithoutDescInput.setText(settings.aiPromptWithoutDesc);
                ui.customCommentsInput.setText(settings.customComments);
            });

            console.log("设置加载成功并更新UI");
            return settings;
        } catch (e) {
            console.error("加载设置失败: " + e);
            toast("加载设置失败，使用默认设置");

            // 更新UI控件为默认值
            ui.run(function() {
                ui.likeTimeMinInput.setText(DEFAULT_SETTINGS.likeTimeMin.toString());
                ui.likeTimeMaxInput.setText(DEFAULT_SETTINGS.likeTimeMax.toString());
                ui.commentTimeMinInput.setText(DEFAULT_SETTINGS.commentTimeMin.toString());
                ui.commentTimeMaxInput.setText(DEFAULT_SETTINGS.commentTimeMax.toString());
                ui.watchTimeMinInput.setText(DEFAULT_SETTINGS.watchTimeMin.toString());
                ui.watchTimeMaxInput.setText(DEFAULT_SETTINGS.watchTimeMax.toString());
                // AI提示词控件默认值
                ui.aiPromptWithDescInput.setText(DEFAULT_SETTINGS.aiPromptWithDesc);
                ui.aiPromptWithoutDescInput.setText(DEFAULT_SETTINGS.aiPromptWithoutDesc);
                ui.customCommentsInput.setText(DEFAULT_SETTINGS.customComments);
            });

            return DEFAULT_SETTINGS;
        }
    } else {
        console.log("设置文件不存在，使用默认设置");

        // 更新UI控件为默认值
        ui.run(function() {
            ui.likeTimeMinInput.setText(DEFAULT_SETTINGS.likeTimeMin.toString());
            ui.likeTimeMaxInput.setText(DEFAULT_SETTINGS.likeTimeMax.toString());
            ui.commentTimeMinInput.setText(DEFAULT_SETTINGS.commentTimeMin.toString());
            ui.commentTimeMaxInput.setText(DEFAULT_SETTINGS.commentTimeMax.toString());
            ui.watchTimeMinInput.setText(DEFAULT_SETTINGS.watchTimeMin.toString());
            ui.watchTimeMaxInput.setText(DEFAULT_SETTINGS.watchTimeMax.toString());
            // AI提示词控件默认值
            ui.aiPromptWithDescInput.setText(DEFAULT_SETTINGS.aiPromptWithDesc);
            ui.aiPromptWithoutDescInput.setText(DEFAULT_SETTINGS.aiPromptWithoutDesc);
            ui.customCommentsInput.setText(DEFAULT_SETTINGS.customComments);
        });

        return DEFAULT_SETTINGS;
    }
}

// 保存设置
function saveSettings() {
    try {
        // 从UI控件获取值
        var settings = {
            likeTimeMin: parseInt(ui.likeTimeMinInput.getText().toString()) || DEFAULT_SETTINGS.likeTimeMin,
            likeTimeMax: parseInt(ui.likeTimeMaxInput.getText().toString()) || DEFAULT_SETTINGS.likeTimeMax,
            commentTimeMin: parseInt(ui.commentTimeMinInput.getText().toString()) || DEFAULT_SETTINGS.commentTimeMin,
            commentTimeMax: parseInt(ui.commentTimeMaxInput.getText().toString()) || DEFAULT_SETTINGS.commentTimeMax,
            watchTimeMin: parseInt(ui.watchTimeMinInput.getText().toString()) || DEFAULT_SETTINGS.watchTimeMin,
            watchTimeMax: parseInt(ui.watchTimeMaxInput.getText().toString()) || DEFAULT_SETTINGS.watchTimeMax,
            // AI提示词设置
            aiPromptWithDesc: ui.aiPromptWithDescInput.getText().toString() || DEFAULT_SETTINGS.aiPromptWithDesc,
            aiPromptWithoutDesc: ui.aiPromptWithoutDescInput.getText().toString() || DEFAULT_SETTINGS.aiPromptWithoutDesc,
            customComments: ui.customCommentsInput.getText().toString() || DEFAULT_SETTINGS.customComments
        };

        console.log("准备保存设置: " + JSON.stringify(settings));

        // 验证设置值的有效性
        if (settings.likeTimeMin > settings.likeTimeMax) {
            toast("点赞最小时间不能大于最大时间");
            return false;
        }
        if (settings.commentTimeMin > settings.commentTimeMax) {
            toast("留言最小时间不能大于最大时间");
            return false;
        }
        if (settings.watchTimeMin > settings.watchTimeMax) {
            toast("观看最小时间不能大于最大时间");
            return false;
        }

        // 确保目录存在
        var settingsDir = files.path("./");
        if (!files.exists(settingsDir)) {
            files.createWithDirs(settingsDir);
            console.log("创建目录: " + settingsDir);
        }

        // 将设置保存到文件
        var settingsJson = JSON.stringify(settings);
        console.log("保存设置到文件: " + SETTINGS_FILE);
        console.log("设置内容: " + settingsJson);

        files.write(SETTINGS_FILE, settingsJson);

        // 验证文件是否成功保存
        if (files.exists(SETTINGS_FILE)) {
            var savedContent = files.read(SETTINGS_FILE);
            console.log("文件保存成功，内容: " + savedContent);
            toast("设置已保存");
            return true;
        } else {
            console.error("文件保存失败，文件不存在");
            toast("设置保存失败，文件不存在");
            return false;
        }
    } catch (e) {
        console.error("保存设置失败: " + e);
        toast("保存设置失败: " + e);
        return false;
    }
}

// 设置主界面布局
ui.layout(
    <vertical h="*">
        <appbar>
            <toolbar title="抖音自动操作设置" titleTextColor="#FFFFFF"/>
        </appbar>
        <ScrollView h="*" scrollbars="vertical">
            <vertical padding="16" h="auto">

        <card margin="10 5" w="*" h="auto" cardCornerRadius="8dp" cardElevation="3dp">
            <vertical padding="16">
                <text text="点赞时间设置 (秒)" textColor="#212121" textSize="16sp"/>
                <horizontal>
                    <text text="最小时间:" textColor="#757575"/>
                    <input id="likeTimeMinInput" hint="3" inputType="number" w="60"/>
                    <text text="最大时间:" textColor="#757575" marginLeft="16"/>
                    <input id="likeTimeMaxInput" hint="17" inputType="number" w="60"/>
                </horizontal>
            </vertical>
        </card>

        <card margin="10 5" w="*" h="auto" cardCornerRadius="8dp" cardElevation="3dp">
            <vertical padding="16">
                <text text="留言时间设置 (秒)" textColor="#212121" textSize="16sp"/>
                <horizontal>
                    <text text="最小时间:" textColor="#757575"/>
                    <input id="commentTimeMinInput" hint="4" inputType="number" w="60"/>
                    <text text="最大时间:" textColor="#757575" marginLeft="16"/>
                    <input id="commentTimeMaxInput" hint="16" inputType="number" w="60"/>
                </horizontal>
            </vertical>
        </card>

        <card margin="10 5" w="*" h="auto" cardCornerRadius="8dp" cardElevation="3dp">
            <vertical padding="16">
                <text text="观看时间设置 (秒)" textColor="#212121" textSize="16sp"/>
                <horizontal>
                    <text text="最小时间:" textColor="#757575"/>
                    <input id="watchTimeMinInput" hint="5" inputType="number" w="60"/>
                    <text text="最大时间:" textColor="#757575" marginLeft="16"/>
                    <input id="watchTimeMaxInput" hint="15" inputType="number" w="60"/>
                </horizontal>
            </vertical>
        </card>

        <card margin="10 5" w="*" h="auto" cardCornerRadius="8dp" cardElevation="3dp">
            <vertical padding="16">
                <text text="有视频介绍的AI提示词" textColor="#212121" textSize="16sp"/>
                <text text="当视频有简介时使用此提示词生成评论" textColor="#757575" textSize="12sp" margin="0 0 8 0"/>
                <ScrollView h="120dp" w="*">
                    <input id="aiPromptWithDescInput" hint="请输入AI提示词..." inputType="textMultiLine" minLines="3" w="*" h="auto"/>
                </ScrollView>
            </vertical>
        </card>

        <card margin="10 5" w="*" h="auto" cardCornerRadius="8dp" cardElevation="3dp">
            <vertical padding="16">
                <text text="没有视频介绍的AI提示词" textColor="#212121" textSize="16sp"/>
                <text text="当视频没有简介时使用此提示词生成评论" textColor="#757575" textSize="12sp" margin="0 0 8 0"/>
                <ScrollView h="120dp" w="*">
                    <input id="aiPromptWithoutDescInput" hint="请输入AI提示词..." inputType="textMultiLine" minLines="3" w="*" h="auto"/>
                </ScrollView>
            </vertical>
        </card>

        <card margin="10 5" w="*" h="auto" cardCornerRadius="8dp" cardElevation="3dp">
            <vertical padding="16">
                <text text="自备评论库" textColor="#212121" textSize="16sp"/>
                <text text="当AI生成失败时使用，每行一条评论" textColor="#757575" textSize="12sp" margin="0 0 8 0"/>
                <ScrollView h="150dp" w="*">
                    <input id="customCommentsInput" hint="请输入自备评论，每行一条..." inputType="textMultiLine" minLines="5" w="*" h="auto"/>
                </ScrollView>
            </vertical>
        </card>

                <horizontal gravity="center" margin="16 10 16 10">
                    <button id="saveButton" text="保存设置" style="Widget.AppCompat.Button.Colored" w="auto"/>
                    <button id="startButton" text="启动" style="Widget.AppCompat.Button.Colored" w="auto" margin="16 0 0 0"/>
                </horizontal>
            </vertical>
        </ScrollView>
    </vertical>
);

// 加载保存的设置
var currentSettings = loadSettings();

// 为保存按钮设置点击事件
ui.saveButton.on("click", function() {
    if (saveSettings()) {
        // 重新加载设置
        currentSettings = loadSettings();
    }
});

// 为启动按钮设置点击事件
ui.startButton.on("click", function() {
    // 先保存当前设置
    if (saveSettings()) {
        toast("启动浮动窗口...");

        // 构建floating.js的路径
        let floatingScriptPath = files.path("./floating.js");

        // 尝试启动floating.js脚本
        try {
            engines.execScriptFile(floatingScriptPath);
            toast("启动浮动窗口成功");
            ui.finish();
        } catch (e) {
            console.error("启动浮动窗口脚本失败: ", e);
            toast("启动浮动窗口脚本失败: " + e);
        }
    }
});

