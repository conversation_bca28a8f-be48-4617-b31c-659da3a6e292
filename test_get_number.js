/**
 * 测试getNumber文件读写功能
 * 验证评论数量的持久化保存和读取
 */

console.log("=== 开始测试getNumber文件读写功能 ==="); // 测试开始日志

// 模拟douyin.js中的getNumber相关代码
var getNumber = 0; // 默认值，将从文件中加载实际值
var GET_NUMBER_FILE = files.cwd() + "/douyin_get_number.txt"; // 评论数量记录文件

console.log("文件路径: " + GET_NUMBER_FILE); // 输出文件路径

// 从文件加载getNumber数值
function loadGetNumber() {
    console.log("开始加载评论数量记录..."); // 开始加载日志
    
    try {
        if (files.exists(GET_NUMBER_FILE)) {
            var numberStr = files.read(GET_NUMBER_FILE); // 读取文件内容
            var savedNumber = parseInt(numberStr.trim()); // 转换为整数
            
            if (!isNaN(savedNumber) && savedNumber >= 0) {
                getNumber = savedNumber; // 更新全局变量
                console.log("✓ 成功加载评论数量: " + getNumber); // 成功日志
                return true; // 返回成功
            } else {
                console.log("⚠️ 文件中的数值无效，使用默认值0"); // 警告日志
                getNumber = 0; // 使用默认值
                return false; // 返回失败
            }
        } else {
            console.log("⚠️ 评论数量文件不存在，使用默认值0"); // 文件不存在日志
            getNumber = 0; // 使用默认值
            return false; // 返回失败
        }
    } catch (error) {
        console.error("✗ 加载评论数量失败: " + error); // 错误日志
        getNumber = 0; // 使用默认值
        return false; // 返回失败
    }
}

// 保存getNumber数值到文件
function saveGetNumber() {
    console.log("保存评论数量到文件: " + getNumber); // 保存日志
    
    try {
        files.write(GET_NUMBER_FILE, getNumber.toString()); // 写入文件
        console.log("✓ 评论数量保存成功"); // 成功日志
        return true; // 返回成功
    } catch (error) {
        console.error("✗ 保存评论数量失败: " + error); // 错误日志
        return false; // 返回失败
    }
}

// 增加评论数量并保存
function incrementGetNumber() {
    getNumber++; // 增加数量
    console.log("评论数量增加到: " + getNumber); // 增加日志
    saveGetNumber(); // 保存到文件
    return getNumber; // 返回当前数量
}

// 重置评论数量
function resetGetNumber() {
    console.log("重置评论数量为0"); // 重置日志
    getNumber = 0; // 重置为0
    saveGetNumber(); // 保存到文件
    return getNumber; // 返回当前数量
}

// 执行测试
console.log("\n=== 测试1: 初始加载 ==="); // 测试1标题
var loadResult = loadGetNumber(); // 加载评论数量
console.log("加载结果: " + (loadResult ? "成功" : "失败")); // 输出加载结果
console.log("当前getNumber值: " + getNumber); // 输出当前值

console.log("\n=== 测试2: 增加数量 ==="); // 测试2标题
for (var i = 1; i <= 5; i++) {
    incrementGetNumber(); // 增加数量
    console.log("第" + i + "次增加后，getNumber = " + getNumber); // 输出增加后的值
}

console.log("\n=== 测试3: 验证文件内容 ==="); // 测试3标题
if (files.exists(GET_NUMBER_FILE)) {
    var fileContent = files.read(GET_NUMBER_FILE); // 读取文件内容
    console.log("文件中保存的内容: '" + fileContent + "'"); // 输出文件内容
    console.log("文件内容转换为数字: " + parseInt(fileContent)); // 输出转换后的数字
} else {
    console.log("✗ 文件不存在"); // 文件不存在
}

console.log("\n=== 测试4: 重新加载验证 ==="); // 测试4标题
var oldValue = getNumber; // 保存旧值
getNumber = -999; // 设置一个明显不同的值
console.log("临时设置getNumber为: " + getNumber); // 输出临时值
loadGetNumber(); // 重新加载
console.log("重新加载后getNumber为: " + getNumber); // 输出重新加载后的值
console.log("是否与之前保存的值一致: " + (getNumber === oldValue ? "是" : "否")); // 验证一致性

console.log("\n=== 测试5: 重置功能 ==="); // 测试5标题
resetGetNumber(); // 重置数量
console.log("重置后getNumber值: " + getNumber); // 输出重置后的值

// 验证重置后的文件内容
if (files.exists(GET_NUMBER_FILE)) {
    var resetFileContent = files.read(GET_NUMBER_FILE); // 读取重置后的文件内容
    console.log("重置后文件内容: '" + resetFileContent + "'"); // 输出重置后的文件内容
}

console.log("\n=== 测试6: 边界情况测试 ==="); // 测试6标题

// 测试大数值
getNumber = 99999; // 设置大数值
saveGetNumber(); // 保存大数值
getNumber = 0; // 重置为0
loadGetNumber(); // 重新加载
console.log("大数值测试 - 保存99999，重新加载得到: " + getNumber); // 输出大数值测试结果

// 测试文件损坏情况
console.log("\n测试文件损坏情况:"); // 测试文件损坏标题
files.write(GET_NUMBER_FILE, "invalid_content"); // 写入无效内容
getNumber = -1; // 设置无效值
var corruptLoadResult = loadGetNumber(); // 尝试加载损坏的文件
console.log("损坏文件加载结果: " + (corruptLoadResult ? "成功" : "失败")); // 输出损坏文件加载结果
console.log("损坏文件加载后getNumber值: " + getNumber); // 输出损坏文件加载后的值

console.log("\n=== 测试完成 ==="); // 测试完成日志
console.log("✅ 所有测试已完成，请检查上述输出结果"); // 完成提示

// 清理测试：恢复为0
resetGetNumber(); // 最终重置为0
console.log("✅ 测试清理完成，评论数量已重置为0"); // 清理完成提示
