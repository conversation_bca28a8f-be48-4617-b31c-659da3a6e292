// 检查并请求悬浮窗权限
if (!floaty.checkPermission()) { // 检查悬浮窗权限是否已授予
    // 如果没有悬浮窗权限，则向用户请求
    floaty.requestPermission(); // 请求悬浮窗权限
    toast("请先授予悬浮窗权限"); // 提示用户授予权限
    exit(); // 退出脚本
} // 结束权限检查的if语句块

// 创建一个浮动窗口
var window = floaty.window( // 定义并创建一个浮动窗口实例
    // 定义浮动窗口的布局
    <frame gravity="center" bg="#00000000"> // 框架布局，居中对齐，背景完全透明
        <vertical> // 垂直布局容器
            <button id="toggleButton" text="折叠" w="38" h="38" style="Widget.AppCompat.Button.Colored" margin="4" bg="#2196F3" radius="30" textSize="12" /> // 创建"展开/折叠"切换按钮
            <vertical id="buttonContainer"> // 可折叠的按钮容器
                <button id="startStopButton" text="开始" w="38" h="38" style="Widget.AppCompat.Button.Colored" margin="4" bg="#FF4081" radius="30" textSize="12" /> // 创建"开始/停止"按钮
                <button id="resetButton" text="日志" w="38" h="38" style="Widget.AppCompat.Button.Colored" margin="4" bg="#FF9800" radius="30" textSize="12" /> // 创建"k"按钮
                <button id="backButton" text="返回" w="38" h="38" style="Widget.AppCompat.Button.Colored" margin="4" bg="#FF4081" radius="30" textSize="12" /> // 创建"返回"按钮
            </vertical> // 结束可折叠按钮容器
        </vertical> // 结束垂直布局
    </frame> // 结束框架布局
); // 结束浮动窗口定义

// 日志窗口
console.show();
console.setSize(200, 200);
console.setTitle("日志");
console.setCanInput(false);
console.setPosition(0, 600);
console.setMaxLines(20);
console.setLogSize(12);
// 设置窗口在退出时自动关闭
window.exitOnClose(); // 设置当浮动窗口关闭时，脚本也退出
// 设置窗口位置和大小，这里设置为较小的值，使其紧凑
window.setPosition(0, 100); // 设置窗口初始位置 (x=0左边距, y=200上边距)
// 定义一个状态变量，记录当前是否在运行中
global.isRunning = false

// 定义折叠状态变量，默认为展开状态（false表示展开，true表示折叠）
var isCollapsed = false;

// 切换按钮点击事件
window.toggleButton.on("click", function () {
    if (isCollapsed) {
        // 当前是折叠状态，点击后展开
        window.buttonContainer.setVisibility(0); // 显示按钮容器（0表示VISIBLE）
        window.toggleButton.setText("折叠"); // 更新按钮文字为"折叠"
        isCollapsed = false; // 更新状态为展开
        console.log("界面已展开"); // 记录日志
    } else {
        // 当前是展开状态，点击后折叠
        window.buttonContainer.setVisibility(8); // 隐藏按钮容器（8表示GONE）
        window.toggleButton.setText("展开"); // 更新按钮文字为"展开"
        isCollapsed = true; // 更新状态为折叠
        console.log("界面已折叠"); // 记录日志
    }
});

// 加载时间设置
function loadTimeSettings() {
    var settingsFile = files.cwd() + "/douyin_settings.json";
    if (files.exists(settingsFile)) {
        try {
            var settingsStr = files.read(settingsFile);
            var settings = JSON.parse(settingsStr);
            console.log("浮动窗口已加载时间设置：");
            console.log("点赞时间: " + settings.likeTimeMin + "-" + settings.likeTimeMax + "秒");
            console.log("留言时间: " + settings.commentTimeMin + "-" + settings.commentTimeMax + "秒");
            console.log("观看时间: " + settings.watchTimeMin + "-" + settings.watchTimeMax + "秒");
            return settings;
        } catch (e) {
            console.error("加载时间设置失败: " + e);
            return null;
        }
    } else {
        console.log("未找到设置文件");
        return null;
    }
}

// 尝试加载时间设置
var timeSettings = loadTimeSettings();

events.broadcast.on('douyin_get', function (title) {
    console.setTitle("日志(" + title + ")");
})

// 监听抖音脚本发送的状态事件
events.broadcast.on("douyin_status", function (data) {
    // 在控制台显示状态信息
    if (typeof data === 'object') {
        // 如果是对象格式，显示状态和消息
        console.log("[状态]: " + data.status + " - " + data.message);

        // 根据状态更新UI
        if (data.status === "stopped") {
            global.isRunning = false;
            ui.run(function () {
                window.startStopButton.setText("开始");
            });
        }
    } else {
        // 如果是字符串格式，直接显示
        console.log("[状态]: " + data);
    }
});


douyin = null; //抖音脚本
// "开始/停止"按钮的点击事件处理
window.startStopButton.on("click", function () {
    if (!global.isRunning) { // 如果当前状态为停止
        // 切换到运行状态
        global.isRunning = true;
        window.startStopButton.setText("停止"); // 将按钮文本设置为"停止"
        // 启动抖音脚本
        toastLog("正在启动抖音脚本...");
        // 使用engines.execScriptFile启动douyin.js
        douyin = engines.execScriptFile("./douyin.js");


    } else { // 如果当前状态为运行中
        global.isRunning = false;
        window.startStopButton.setText("开始"); // 将按钮文本设置为"开始"
        // 停止抖音脚本
        toastLog("正在停止抖音脚本...");
        douyin.getEngine().forceStop();
    }
});
var consoleIs=false;
// "日志"按钮的点击事件处理
window.resetButton.on("click", function () {
    if(consoleIs){
        console.show();
    }else{
        console.hide()
    }
    consoleIs=!consoleIs;
});

// "返回"按钮的点击事件处理
window.backButton.on("click", function () {
    toastLog("点击了返回按钮，关闭悬浮窗");
    if (global.isRunning) {
        toastLog("正在停止抖音脚本...");
        douyin.getEngine().forceStop();
    }
    setTimeout(() => {
        console.hide()
        engines.execScriptFile('./main.js');
        window.close()
    }, 1000)
});
setInterval(() => { }, 1000);